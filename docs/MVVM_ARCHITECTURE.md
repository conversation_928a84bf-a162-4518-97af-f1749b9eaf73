# McpActivity MVVM 架构重构说明

## 🎯 重构目标

将原本功能臃肿的 McpActivity 重构为清晰的 MVVM 架构，实现：
- **职责分离**：View 只负责UI展示，ViewModel 处理业务逻辑，Repository 管理数据
- **可测试性**：业务逻辑从 Activity 中分离，便于单元测试
- **可维护性**：代码结构清晰，易于理解和修改
- **可扩展性**：新功能可以轻松添加到对应层级

## 🏗️ 架构分层

### 1. View 层 (McpActivity)
**职责**：仅负责UI展示和用户交互
- 初始化UI组件
- 设置点击事件监听
- 观察ViewModel状态变化
- 更新UI显示

**核心方法**：
- `setupClickListeners()` - 设置用户交互
- `observeViewModelStates()` - 观察状态变化
- `updateSttUI()` - 更新语音识别UI
- `updateChatMessages()` - 更新聊天消息
- `updateUIState()` - 更新通用UI状态

### 2. ViewModel 层 (McpViewModel)
**职责**：管理UI状态和业务逻辑
- 处理用户操作（录音、滚动等）
- 管理STT防抖逻辑
- 协调Repository数据操作
- 维护UI状态

**核心状态**：
- `sttState` - 语音识别状态
- `chatMessages` - 聊天消息列表
- `uiState` - UI显示状态
- `audioState` - 音频播放状态

**核心方法**：
- `toggleRecording()` - 切换录音状态
- `handleSttText()` - 处理语音识别文本
- `requestAiResponse()` - 请求AI回复
- `scrollToBottom()` - 滚动控制

### 3. Repository 层 (McpRepository)
**职责**：管理所有数据源和外部服务
- STT语音识别管理
- WebSocket连接管理
- 音频播放管理
- SSE请求处理
- 本地指令检查

**核心功能**：
- `startRecording()` / `stopRecording()` - 录音控制
- `sendSseRequest()` - SSE请求
- `manualReconnectWebSocket()` - WebSocket重连
- `checkLocalCommand()` - 本地指令检查

### 4. Manager 层
**职责**：封装具体的功能模块
- `SttManager` - 语音识别管理
- 未来可扩展：`AudioManager`、`WebSocketManager` 等

### 5. Model 层
**职责**：定义数据结构
- `McpUiState` - UI状态数据
- `SttState` - 语音识别状态
- `AudioState` - 音频状态
- `WebSocketState` - WebSocket状态

## 🔄 数据流向

```
User Interaction → View → ViewModel → Repository → External Services
                    ↑        ↑           ↑
                    ←        ←           ← (State Updates)
```

### 典型流程示例：语音识别

1. **用户点击录音按钮**
   ```
   McpActivity.setupClickListeners() 
   → viewModel.toggleRecording()
   ```

2. **ViewModel处理录音逻辑**
   ```
   McpViewModel.toggleRecording() 
   → repository.startRecording()
   → _sttState.value = sttState.copy(isRecording = true)
   ```

3. **Repository管理STT服务**
   ```
   McpRepository.startRecording() 
   → sttManager.startRecording()
   → 回调语音识别结果
   ```

4. **ViewModel处理识别结果**
   ```
   handleSttText() 
   → 防抖处理 
   → 更新聊天消息
   → 请求AI回复
   ```

5. **View更新UI**
   ```
   observeViewModelStates() 
   → updateSttUI() 
   → updateChatMessages()
   ```

## 🎨 重构前后对比

### 重构前 (原McpActivity)
```kotlin
class McpActivity {
    // ❌ 所有功能混在一起
    private var sttDebounceJob: Job? = null
    private val sttTextBuilder = StringBuilder()
    private val markdownBuilder = StringBuilder()
    
    // ❌ 业务逻辑直接在Activity中
    private fun handleSttTextWithDebounce(text: String) {
        // 复杂的防抖逻辑
        // 直接操作UI
        // 直接调用网络请求
    }
    
    // ❌ 初始化代码冗长
    override fun initCommonListener() {
        // 100+ 行初始化代码
    }
}
```

### 重构后 (MVVM架构)
```kotlin
// ✅ View层：只负责UI
class McpActivity {
    private lateinit var viewModel: McpViewModel
    
    private fun setupClickListeners() {
        mBinding.add.singleClick {
            viewModel.toggleRecording() // 简洁的调用
        }
    }
}

// ✅ ViewModel层：管理业务逻辑
class McpViewModel {
    fun toggleRecording() {
        // 清晰的业务逻辑
    }
}

// ✅ Repository层：管理数据
class McpRepository {
    suspend fun startRecording() {
        // 专注的数据管理
    }
}
```

## 🧪 测试策略

### 1. 单元测试
- **ViewModel测试**：验证业务逻辑正确性
- **Repository测试**：验证数据操作
- **Manager测试**：验证具体功能模块

### 2. 集成测试
- **View-ViewModel集成**：验证状态绑定
- **ViewModel-Repository集成**：验证数据流

### 3. UI测试
- **用户交互测试**：验证完整用户流程

## 🚀 扩展性

### 新功能添加流程
1. **定义数据模型** (Model层)
2. **在Repository中添加数据操作** (Repository层)
3. **在ViewModel中添加业务逻辑** (ViewModel层)
4. **在View中添加UI更新** (View层)

### 示例：添加文件上传功能
```kotlin
// 1. Model
data class FileUploadState(
    val isUploading: Boolean = false,
    val progress: Float = 0f,
    val error: String? = null
)

// 2. Repository
suspend fun uploadFile(file: File): Result<String>

// 3. ViewModel
fun uploadFile(file: File) {
    launchSafely {
        _fileUploadState.value = FileUploadState(isUploading = true)
        val result = repository.uploadFile(file)
        // 处理结果
    }
}

// 4. View
private fun observeFileUploadState() {
    viewModel.fileUploadState.collect { state ->
        updateUploadUI(state)
    }
}
```

## 📋 最佳实践

1. **单一职责**：每个类只负责一个明确的职责
2. **依赖注入**：使用Factory模式创建ViewModel
3. **状态管理**：使用StateFlow管理状态
4. **错误处理**：统一的异常处理机制
5. **资源管理**：及时清理资源，避免内存泄漏
6. **测试友好**：业务逻辑与UI分离，便于测试

## 🔧 技术栈

- **架构模式**：MVVM
- **状态管理**：StateFlow + Coroutines
- **依赖注入**：Manual DI (可升级为Hilt)
- **异步处理**：Kotlin Coroutines
- **UI绑定**：DataBinding
- **测试框架**：JUnit + Mockito

这个重构大大提升了代码的可维护性、可测试性和可扩展性，为后续功能开发奠定了良好的架构基础。
