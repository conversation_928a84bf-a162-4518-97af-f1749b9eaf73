package com.haoxue.libcommon.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.lazy.library.logging.Logcat

/**
 * ViewModel 基类
 * 提供通用的状态管理和错误处理
 */
abstract class BaseViewModel : ViewModel() {

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // 通用的异常处理器
    protected val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        Logcat.e("ViewModel异常", exception)
        _error.value = exception.message ?: "未知错误"
        _isLoading.value = false
    }

    /**
     * 安全执行协程任务
     */
    protected fun launchSafely(
        showLoading: Boolean = true,
        block: suspend () -> Unit
    ) {
        viewModelScope.launch(exceptionHandler) {
            try {
                if (showLoading) _isLoading.value = true
                _error.value = null
                block()
            } finally {
                if (showLoading) _isLoading.value = false
            }
        }
    }

    /**
     * 设置错误信息
     */
    protected fun setError(message: String) {
        _error.value = message
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * 设置加载状态
     */
    protected fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
}
