package com.haoxue.sportai

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.LifecycleCoroutineScope
import com.haoxue.sportai.repository.McpRepository
import com.haoxue.sportai.viewmodel.McpViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.runBlockingTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations

/**
 * McpViewModel 单元测试
 * 验证MVVM架构重构的正确性
 */
@ExperimentalCoroutinesApi
class McpViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = TestCoroutineDispatcher()

    @Mock
    private lateinit var mockRepository: McpRepository

    private lateinit var viewModel: McpViewModel

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        viewModel = McpViewModel(mockRepository)
    }

    @Test
    fun `test toggle recording changes state`() = runBlockingTest {
        // Given
        val initialState = viewModel.sttState.value
        assert(!initialState.isRecording)

        // When
        viewModel.toggleRecording()

        // Then
        // 这里需要mock repository的行为
        // 实际测试中需要验证状态变化
    }

    @Test
    fun `test scroll to bottom updates UI state`() = runBlockingTest {
        // Given
        val initialState = viewModel.uiState.value
        assert(!initialState.shouldScrollToBottom)

        // When
        viewModel.scrollToBottom()

        // Then
        val updatedState = viewModel.uiState.value
        assert(updatedState.shouldScrollToBottom)
    }

    @Test
    fun `test manual reconnect triggers repository`() = runBlockingTest {
        // When
        viewModel.manualReconnectWebSocket()

        // Then
        // 验证repository的manualReconnectWebSocket方法被调用
        // verify(mockRepository).manualReconnectWebSocket()
    }
}
