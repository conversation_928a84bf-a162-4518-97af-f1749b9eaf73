package com.haoxue.sportai.repository

import android.content.Context
import androidx.lifecycle.LifecycleCoroutineScope
import com.haoxue.libcommon.ConstData
import com.haoxue.libcommon.markdown.BasicMarkdownUtils
import com.haoxue.libcommon.utils.AudioPlaybackUtils
import com.haoxue.libcommon.utils.SafeWebSocketUtils
import com.haoxue.libcommon.utils.SseUtils
import com.haoxue.mcpserver.ToolsUtils
import com.haoxue.sportai.manager.SttManager
import com.haoxue.sportai.model.AudioPlaybackState
import com.haoxue.sportai.model.WebSocketState
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * MCP 数据仓库
 * 负责管理所有数据源和业务逻辑
 */
class McpRepository(
    private val context: Context,
    private val lifecycleScope: LifecycleCoroutineScope
) {

    // STT 管理器
    private val sttManager = SttManager(context)

    // WebSocket 状态
    private val _webSocketState = MutableStateFlow(WebSocketState())
    val webSocketState: StateFlow<WebSocketState> = _webSocketState.asStateFlow()

    // 音频播放状态
    private val _audioPlaybackState = MutableStateFlow(AudioPlaybackState())
    val audioPlaybackState: StateFlow<AudioPlaybackState> = _audioPlaybackState.asStateFlow()

    /**
     * 初始化仓库
     */
    fun initialize() {
        initializeAudioPlayback()
        initializeWebSocket()
    }

    /**
     * 初始化音频播放
     */
    private fun initializeAudioPlayback() {
        AudioPlaybackUtils.initialize(context)

        // 设置音频播放监听器
        AudioPlaybackUtils.setOnQueueChangedListener { queueSize ->
            _audioPlaybackState.value = _audioPlaybackState.value.copy(queueSize = queueSize)
            Logcat.d("音频队列长度变化: $queueSize")
        }

        AudioPlaybackUtils.setOnPlaybackStateChangedListener { state ->
            val isPlaying = state == "PLAYING"
            _audioPlaybackState.value = _audioPlaybackState.value.copy(isPlaying = isPlaying)
            Logcat.d("音频播放状态变化: $state")
        }

        AudioPlaybackUtils.setOnPlaybackErrorListener { error, exception ->
            _audioPlaybackState.value = _audioPlaybackState.value.copy(error = error)
            Logcat.e("音频播放错误: $error", exception)
        }
    }

    /**
     * 初始化WebSocket连接
     */
    private fun initializeWebSocket() {
        // 配置WebSocket自动重连
        SafeWebSocketUtils.configureReconnect(
            enabled = true,
            maxAttempts = -1,      // 无限重连
            intervalMs = 2000L,    // 基础间隔2秒
            maxIntervalMs = 10000L // 最大间隔10秒
        )

        // 连接WebSocket
        SafeWebSocketUtils.safeConnect(
            lifecycleScope,
            ConstData.TTS_WEBSOCKET_URL,
            onMessage = { message ->
                Logcat.d("WebSocket 收到音频消息: $message")
                AudioPlaybackUtils.playAudio(message)
            },
            onConnected = {
                _webSocketState.value = _webSocketState.value.copy(
                    isConnected = true,
                    reconnectInfo = SafeWebSocketUtils.getReconnectInfo()
                )
                Logcat.d("🔗 WebSocket 音频连接成功")
            },
            onDisconnected = {
                _webSocketState.value = _webSocketState.value.copy(isConnected = false)
                Logcat.d("❌ WebSocket 音频连接断开")
            },
            onError = { error ->
                _webSocketState.value = _webSocketState.value.copy(
                    isConnected = false,
                    lastError = error
                )
                Logcat.e("🚨 WebSocket 音频连接错误: $error")
            }
        )
    }

    /**
     * 开始录音
     */
    suspend fun startRecording(onTextRecognized: (String) -> Unit) {
        sttManager.startRecording(onTextRecognized)
    }

    /**
     * 停止录音
     */
    suspend fun stopRecording() {
        sttManager.stopRecording()
    }

    /**
     * 停止音频播放
     */
    fun stopAudioPlayback() {
        AudioPlaybackUtils.stop()
    }

    /**
     * 检查本地指令
     */
    fun checkLocalCommand(text: String): String? {
        return ToolsUtils.interceptFun(text)
    }

    /**
     * 判断是否为Markdown内容
     */
    fun isMarkdownContent(content: String): Boolean {
        return BasicMarkdownUtils.isMarkdown(content)
    }

    /**
     * 发送SSE请求
     */
    suspend fun sendSseRequest(
        question: String,
        onMessage: (String) -> Unit,
        onComplete: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        SseUtils.quickAsk(
            lifecycleScope,
            question,
            onComplete = onComplete,
            onError = onError,
            onMessage = onMessage
        )
    }

    /**
     * 手动重连WebSocket
     */
    fun manualReconnectWebSocket() {
        SafeWebSocketUtils.manualReconnect()
        _webSocketState.value = _webSocketState.value.copy(
            reconnectInfo = "手动重连中..."
        )
        Logcat.d("手动触发WebSocket重连")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        sttManager.release()
        AudioPlaybackUtils.release()
    }
}
