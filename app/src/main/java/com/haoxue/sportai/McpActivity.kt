package com.haoxue.sportai

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.Lifecycle
import com.haoxue.libcommon.markdown.BasicMarkdownRenderer
import com.haoxue.libcommon.markdown.BasicChatAdapter
import com.haoxue.libcommon.markdown.BasicMarkdownRecyclerUtils
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.mcpserver.McpServerHelper
import com.haoxue.sportai.databinding.ActivityMcpBinding
import com.haoxue.sportai.viewmodel.McpViewModel
import com.haoxue.sportai.viewmodel.McpViewModelFactory
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.launch

/**
 * MCP Activity - 使用 MVVM 架构重构
 * 职责：仅负责UI展示和用户交互，业务逻辑全部移至ViewModel
 */
class McpActivity : BaseActivity<ActivityMcpBinding>(R.layout.activity_mcp) {
    
    // ViewModel
    private lateinit var viewModel: McpViewModel
    
    // 聊天适配器
    private lateinit var chatAdapter: BasicChatAdapter

    override fun initCommonData() {
        // 初始化 ViewModel
        val factory = McpViewModelFactory(this, lifecycleScope)
        viewModel = ViewModelProvider(this, factory)[McpViewModel::class.java]
        
        // 设置点击事件
        setupClickListeners()
        
        // 观察ViewModel状态
        observeViewModelStates()
    }

    /**
     * 设置点击事件
     */
    private fun setupClickListeners() {
        mBinding.add.singleClick {
            viewModel.toggleRecording()
        }
        
        mBinding.scrollToBottomFab.singleClick {
            viewModel.scrollToBottom()
        }

        mBinding.scrollToBottomFab.setOnLongClickListener {
            viewModel.manualReconnectWebSocket()
            true
        }
    }

    /**
     * 观察ViewModel状态变化
     */
    private fun observeViewModelStates() {
        // 观察STT状态
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.sttState.collect { sttState ->
                    updateSttUI(sttState)
                }
            }
        }

        // 观察聊天消息
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.chatMessages.collect { messages ->
                    updateChatMessages(messages)
                }
            }
        }

        // 观察UI状态
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    updateUIState(uiState)
                }
            }
        }

        // 观察音频状态
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.audioState.collect { audioState ->
                    updateAudioUI(audioState)
                }
            }
        }

        // 观察错误状态
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let {
                        Logcat.e("ViewModel错误: $it")
                        // 这里可以显示错误提示
                        viewModel.clearError()
                    }
                }
            }
        }
    }

    /**
     * 更新STT UI状态
     */
    private fun updateSttUI(sttState: com.haoxue.sportai.model.SttState) {
        mBinding.add.text = if (sttState.isRecording) "结束" else "开始"
        Logcat.d("STT状态更新: 录音=${sttState.isRecording}, 文本=${sttState.currentText}")
    }

    /**
     * 更新聊天消息
     */
    private fun updateChatMessages(messages: List<BasicChatAdapter.ChatMessage>) {
        if (::chatAdapter.isInitialized) {
            chatAdapter.updateMessages(messages)
            if (messages.isNotEmpty()) {
                BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
            }
        }
    }

    /**
     * 更新UI状态
     */
    private fun updateUIState(uiState: com.haoxue.sportai.model.McpUiState) {
        if (uiState.shouldScrollToBottom && ::chatAdapter.isInitialized) {
            BasicMarkdownRecyclerUtils.forceScrollToBottom(mBinding.recyclerView)
            viewModel.resetScrollState()
        }
        
        // 控制滚动按钮显示
        mBinding.scrollToBottomFab.visibility = if (uiState.showScrollButton) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }
        
        Logcat.d("WebSocket连接状态: ${uiState.isWebSocketConnected}")
        if (uiState.reconnectInfo.isNotEmpty()) {
            Logcat.d("重连信息: ${uiState.reconnectInfo}")
        }
    }

    /**
     * 更新音频UI状态
     */
    private fun updateAudioUI(audioState: com.haoxue.sportai.model.AudioState) {
        Logcat.d("音频状态: 播放=${audioState.isPlaying}, 队列=${audioState.queueSize}")
    }

    override fun initCommonListener() {
        // 初始化MCP服务器
        McpServerHelper.init(this)
        
        // 初始化基础版 Markdown 渲染器
        BasicMarkdownRenderer.initialize(this)

        // 设置基础聊天适配器
        chatAdapter = BasicMarkdownRecyclerUtils.setupChatRecyclerView(
            mBinding.recyclerView,
            this,
            onItemClick = { message, position ->
                // 点击消息的处理
                Logcat.d("点击消息: ${message.content}")
            },
            onScrollStateChanged = { shouldShowScrollButton ->
                // 通知ViewModel更新滚动按钮状态
                viewModel.updateScrollButtonVisibility(shouldShowScrollButton)
            }
        )
        
        // 初始化Repository（通过ViewModel）
        viewModel.initializeRepository()
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 清理RecyclerView滚动监听器
        if (::chatAdapter.isInitialized) {
            BasicMarkdownRecyclerUtils.cleanup(mBinding.recyclerView)
        }
        
        // 停止MCP服务器
        McpServerHelper.stopServer()
    }

    override fun requestCommonData() {
        // 这里可以添加初始数据请求逻辑
    }
}
